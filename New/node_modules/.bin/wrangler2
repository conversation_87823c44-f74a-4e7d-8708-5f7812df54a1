#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/wrangler@4.26.0/node_modules/wrangler/bin/node_modules:/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/wrangler@4.26.0/node_modules/wrangler/node_modules:/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/wrangler@4.26.0/node_modules:/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/wrangler@4.26.0/node_modules/wrangler/bin/node_modules:/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/wrangler@4.26.0/node_modules/wrangler/node_modules:/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/wrangler@4.26.0/node_modules:/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../wrangler/bin/wrangler.js" "$@"
else
  exec node  "$basedir/../wrangler/bin/wrangler.js" "$@"
fi
