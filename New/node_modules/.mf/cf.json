{"requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 13335, "clientAcceptEncoding": "gzip, deflate, br", "verifiedBotCategory": "", "country": "BD", "isEUCountry": false, "region": "Dhaka Division", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "kNweagHloSoclijfr46AU6mUj5dlftyn6Pmm0tj/lQo=", "tlsExportedAuthenticator": {"clientFinished": "a0235d54aea3ac1aaa7d6242dc78933422c096333d15ca7cbd4222a2059ef353c10caa99bb5c8aeb9751f462ffe7fba2", "clientHandshake": "2fd27fb484405d90503708e6edcd6627ac40527f3e884fb9acc5d6d51141c83e758df1fad84dc6c00aed02261b3c9b02", "serverHandshake": "c91fd0ab5ea422560366057f59e75517b217ceb195f6349547b261c4a6efa1f50437369204f3e2af9360431c52225801", "serverFinished": "f58b883ac3a1fb01a9e45ee5ebc705351976aed88370fa232d1af68dbbcf696ec323380c22fbbea6d0bb132245f38afe"}, "tlsClientHelloLength": "386", "colo": "SIN", "timezone": "Asia/Dhaka", "longitude": "90.40744", "latitude": "23.71040", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "1000", "city": "Dhaka", "tlsVersion": "TLSv1.3", "regionCode": "C", "asOrganization": "Cloudflare, Inc.", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}