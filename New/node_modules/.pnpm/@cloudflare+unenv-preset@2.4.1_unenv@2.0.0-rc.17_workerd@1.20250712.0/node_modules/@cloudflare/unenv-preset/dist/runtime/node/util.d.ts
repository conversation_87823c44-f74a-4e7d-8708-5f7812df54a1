export { _errnoException, _exceptionWithHostPort, getSystemErrorMap, getSystemErrorName, isBoolean, isBuffer, isDate, isError, isFunction, isNull, isNullOrUndefined, isNumber, isObject, isPrimitive, isRegExp, isString, isSymbol, isUndefined, parseEnv, styleText, } from "unenv/node/util";
export declare const MIMEParams: typeof import("util").MIMEParams, MIMEType: typeof import("util").MIMEType, TextDecoder: typeof import("util").TextDecoder, TextEncoder: typeof import("util").TextEncoder, _extend: any, aborted: typeof import("util").aborted, callbackify: typeof import("util").callbackify, debug: typeof import("util").debuglog, debuglog: typeof import("util").debuglog, deprecate: typeof import("util").deprecate, format: typeof import("util").format, formatWithOptions: typeof import("util").formatWithOptions, getCallSite: any, inherits: typeof import("util").inherits, inspect: typeof import("util").inspect, isArray: typeof import("util").isArray, isDeepStrictEqual: typeof import("util").isDeepStrictEqual, log: typeof import("util").log, parseArgs: typeof import("util").parseArgs, promisify: typeof import("util").promisify, stripVTControlCharacters: typeof import("util").stripVTControlCharacters, toUSVString: typeof import("util").toUSVString, transferableAbortController: typeof import("util").transferableAbortController, transferableAbortSignal: typeof import("util").transferableAbortSignal;
export declare const types: typeof import("node:util/types");
declare const _default: any;
export default _default;
