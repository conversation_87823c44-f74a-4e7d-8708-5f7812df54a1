{"name": "@neondatabase/serverless", "version": "1.0.1", "author": "Neon", "description": "node-postgres for serverless environments from neon.tech", "exports": {"require": "./index.js", "import": "./index.mjs"}, "license": "MIT", "homepage": "https://neon.tech", "repository": {"type": "git", "url": "https://github.com/neondatabase/serverless"}, "keywords": ["Neon", "serverless", "Postgres", "PostgreSQL", "pg", "database", "SQL", "edge", "workers", "Cloudflare", "Vercel", "RDBMS", "WebSocket", "https"], "files": ["index.js", "index.d.ts", "index.mjs", "index.d.mts", "README.md", "LICENSE", "CHANGELOG.md", "CONFIG.md", "DEPLOY.md"], "engines": {"node": ">=19.0.0"}, "scripts": {"build": "./build.sh", "preversion": "([[ $(git branch --show-current) = 'main' ]] || echo 'Must be on `main`') && git pull && npm run format && npm run test", "version": "(grep $npm_package_version CHANGELOG.md || 'Update CHANGELOG.md') && git checkout -b release/v$npm_package_version && jq --arg v $npm_package_version '.version = $v' jsr.json > build/jsr.json && mv build/jsr.json jsr.json && git add jsr.json index.js index.d.ts index.mjs index.d.mts", "postversion": "git push --follow-tags origin release/v$npm_package_version && echo 'Release $npm_package_version created. Run `npm publish` and `npx jsr publish` once PR is merged into `main`.'", "format": "prettier -c .", "format:fix": "prettier -w .", "test": "touch src && npm run test:node && npm run test:edge && npm run test:bun && npm run test:deno && npm run test:packages && npm run test:cloudflare && npm run test:vercel && npm run test:browser", "test:node": "npm run build && vitest run --dir=tests/cli --environment=node --typecheck --config=tests/vite.config.mts", "test:edge": "npm run build && vitest run --dir=tests/cli --environment=edge-runtime --config=tests/vite.config.mts", "test:bun": "npm run build && bun run --env-file=.env.test tests/basic/basic.ts", "test:deno": "npm run build && deno run --env-file=.env.test --allow-all tests/basic/basic.ts", "test:packages": "npm run build && vitest run --dir=tests/packages --environment=node --config=tests/vite.config.mts", "test:cloudflare": "npm run build && vitest run --dir=tests/cloudflare --config=tests/vite.config.mts", "test:vercel": "npm run build && vitest run --dir=tests/vercel --config=tests/vite.config.mts", "test:browser": "npm run build && playwright install --with-deps firefox chromium && vitest run --dir=tests/browser --browser --config=tests/browser/vite.config.mts"}, "dependencies": {"@types/node": "^22.15.30", "@types/pg": "^8.8.0"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241230.0", "@edge-runtime/vm": "^5.0.0", "@microsoft/api-extractor": "^7.48.1", "@neondatabase/serverless": "file:.", "@prisma/adapter-neon": "^6.5.0", "@prisma/client": "^6.2.1", "@types/events": "^3.0.0", "@types/ws": "^8.5.4", "@vercel/edge": "^1.2.1", "@vercel/sdk": "^1.2.2", "@vitest/browser": "^3.0.7", "assert": "file:src/shims/assert", "buffer": "^6.0.3", "crypto": "file:src/shims/crypto", "dns": "file:src/shims/dns", "drizzle-orm": "^0.44.2", "esbuild": "^0.25.0", "events": "^3.3.0", "fast-equals": "^5.0.1", "fs": "file:src/shims/fs", "hextreme": "^1.0.7", "jsr": "^0.13.3", "net": "file:src/shims/net", "node-fetch": "^3.3.2", "npm-run-all": "^4.1.5", "path": "file:src/shims/path", "pg": "8.8.0", "pg-native": "file:src/shims/pg-native", "playwright": "^1.49.1", "prettier": "^3.4.1", "prisma": "^6.2.1", "stream": "file:src/shims/stream", "string_decoder": "file:src/shims/string_decoder", "subtls": "^0.5.0", "tls": "file:src/shims/tls", "typescript": "^5.0.4", "undici": "^7.1.0", "url": "file:src/shims/url", "util": "file:src/shims/util", "vitest": "^3.0.7", "wrangler": "^4.0.0", "ws": "^8.12.1"}, "overrides": {"pg-connection-string": "2.5.0", "@neondatabase/serverless": "file:."}}