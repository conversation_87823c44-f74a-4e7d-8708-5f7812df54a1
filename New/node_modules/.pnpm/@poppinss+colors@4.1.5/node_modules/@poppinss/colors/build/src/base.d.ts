/**
 * @poppinss/colors
 *
 * (c) Poppinss
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
/**
 * Abstract implementation of the colors class.
 */
export declare abstract class Colors {
    protected abstract transform(transformation: string): this;
    protected abstract transform(transformation: string, text: string | number): string;
    protected abstract transform(transformation: string, text?: string | number): this | string;
    black(): this;
    black(text: string): string;
    red(): this;
    red(text: string): string;
    green(): this;
    green(text: string): string;
    yellow(): this;
    yellow(text: string): string;
    blue(): this;
    blue(text: string): string;
    magenta(): this;
    magenta(text: string): string;
    cyan(): this;
    cyan(text: string): string;
    white(): this;
    white(text: string): string;
    gray(): this;
    gray(text: string): string;
    grey(): this;
    grey(text: string): string;
    bgBlack(): this;
    bgBlack(text: string): string;
    bgRed(): this;
    bgRed(text: string): string;
    bgGreen(): this;
    bgGreen(text: string): string;
    bgYellow(): this;
    bgYellow(text: string): string;
    bgBlue(): this;
    bgBlue(text: string): string;
    bgMagenta(): this;
    bgMagenta(text: string): string;
    bgCyan(): this;
    bgCyan(text: string): string;
    bgWhite(): this;
    bgWhite(text: string): string;
    reset(): this;
    reset(text: string): string;
    bold(): this;
    bold(text: string): string;
    dim(): this;
    dim(text: string): string;
    italic(): this;
    italic(text: string): string;
    underline(): this;
    underline(text: string): string;
    inverse(): this;
    inverse(text: string): string;
    hidden(): this;
    hidden(text: string): string;
    strikethrough(): this;
    strikethrough(text: string): string;
}
