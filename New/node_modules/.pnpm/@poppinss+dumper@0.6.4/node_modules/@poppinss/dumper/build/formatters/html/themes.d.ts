/**
 * Default styles to use for pretty printing
 * the HTML output.
 */
export declare const themes: {
    nightOwl: {
        pre: string;
        toggle: string;
        braces: string;
        brackets: string;
        number: string;
        bigInt: string;
        boolean: string;
        string: string;
        null: string;
        undefined: string;
        prototypeLabel: string;
        symbol: string;
        regex: string;
        date: string;
        buffer: string;
        functionLabel: string;
        arrayLabel: string;
        objectLabel: string;
        mapLabel: string;
        setLabel: string;
        objectKey: string;
        objectKeyPrefix: string;
        classLabel: string;
        collapseLabel: string;
        getterLabel: string;
        circularLabel: string;
        weakSetLabel: string;
        weakRefLabel: string;
        weakMapLabel: string;
        observableLabel: string;
        promiseLabel: string;
        generatorLabel: string;
        blobLabel: string;
        unknownLabel: string;
    };
    minLight: {
        pre: string;
        toggle: string;
        braces: string;
        brackets: string;
        number: string;
        bigInt: string;
        boolean: string;
        string: string;
        null: string;
        undefined: string;
        prototypeLabel: string;
        symbol: string;
        regex: string;
        date: string;
        buffer: string;
        functionLabel: string;
        arrayLabel: string;
        objectLabel: string;
        mapLabel: string;
        setLabel: string;
        objectKey: string;
        objectKeyPrefix: string;
        classLabel: string;
        collapseLabel: string;
        getterLabel: string;
        circularLabel: string;
        weakSetLabel: string;
        weakRefLabel: string;
        weakMapLabel: string;
        observableLabel: string;
        promiseLabel: string;
        generatorLabel: string;
        blobLabel: string;
        unknownLabel: string;
    };
    catppuccin: {
        pre: string;
        toggle: string;
        braces: string;
        brackets: string;
        number: string;
        bigInt: string;
        boolean: string;
        string: string;
        null: string;
        undefined: string;
        prototypeLabel: string;
        symbol: string;
        regex: string;
        date: string;
        buffer: string;
        functionLabel: string;
        arrayLabel: string;
        objectLabel: string;
        mapLabel: string;
        setLabel: string;
        objectKey: string;
        objectKeyPrefix: string;
        classLabel: string;
        collapseLabel: string;
        getterLabel: string;
        circularLabel: string;
        weakSetLabel: string;
        weakRefLabel: string;
        weakMapLabel: string;
        observableLabel: string;
        promiseLabel: string;
        generatorLabel: string;
        blobLabel: string;
        unknownLabel: string;
    };
    /**
     * Following is the list of defined variables
      --pre-bg-color
      --pre-fg-color
      --toggle-fg-color
      --braces-fg-color
      --brackets-fg-color
      --dt-number-fg-color
      --dt-bigint-fg-color
      --dt-boolean-fg-color
      --dt-string-fg-color
      --dt-null-fg-color
      --dt-undefined-fg-color
      --prototype-label-fg-color
      --dt-symbol-fg-color
      --dt-regex-fg-color
      --dt-date-fg-color
      --dt-buffer-fg-color
      --function-label-fg-color
      --array-label-fg-color
      --object-label-fg-color
      --map-label-fg-color
      --set-label-fg-color
      --object-key-fg-color
      --object-key-prefix-fg-color
      --class-label-fg-color
      --collpase-label-fg-color
      --getter-label-fg-color
      --circular-label-fg-color
      --weakset-label-fg-color
      --weakref-label-fg-color
      --weakmap-label-fg-color
      --observable-label-fg-color
      --promise-label-fg-color
      --generator-label-fg-color
      --blob-label-fg-color
      --unknown-label-fg-color
     */
    cssVariables: {
        pre: string;
        toggle: string;
        braces: string;
        brackets: string;
        number: string;
        bigInt: string;
        boolean: string;
        string: string;
        null: string;
        undefined: string;
        prototypeLabel: string;
        symbol: string;
        regex: string;
        date: string;
        buffer: string;
        functionLabel: string;
        arrayLabel: string;
        objectLabel: string;
        mapLabel: string;
        setLabel: string;
        objectKey: string;
        objectKeyPrefix: string;
        classLabel: string;
        collapseLabel: string;
        getterLabel: string;
        circularLabel: string;
        weakSetLabel: string;
        weakRefLabel: string;
        weakMapLabel: string;
        observableLabel: string;
        promiseLabel: string;
        generatorLabel: string;
        blobLabel: string;
        unknownLabel: string;
    };
};
