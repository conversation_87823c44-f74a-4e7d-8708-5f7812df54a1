var l=Object.defineProperty;var p=Object.getOwnPropertyDescriptor;var o=Object.getOwnPropertyNames;var i=Object.prototype.hasOwnProperty;var n=(t,e)=>{for(var s in e)l(t,s,{get:e[s],enumerable:!0})},m=(t,e,s,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of o(e))!i.call(t,a)&&a!==s&&l(t,a,{get:()=>e[a],enumerable:!(r=p(e,a))||r.enumerable});return t};var d=t=>m(l({},"__esModule",{value:!0}),t);var u={};n(u,{default:()=>h});module.exports=d(u);var c={type:"var",match:/\$\w+|\${[^}]*}|\$\([^)]*\)/g},h=[{sub:"todo",match:/#.*/g},{type:"str",match:/(["'])((?!\1)[^\r\n\\]|\\[^])*\1?/g,sub:[c]},{type:"oper",match:/(?<=\s|^)\.*\/[a-z/_.-]+/gi},{type:"kwd",match:/\s-[a-zA-Z]+|$<|[&|;]+|\b(unset|readonly|shift|export|if|fi|else|elif|while|do|done|for|until|case|esac|break|continue|exit|return|trap|wait|eval|exec|then|declare|enable|local|select|typeset|time|add|remove|install|update|delete)(?=\s|$)/g},{expand:"num"},{type:"func",match:/(?<=(^|\||\&\&|\;)\s*)[a-z_.-]+(?=\s|$)/gmi},{type:"bool",match:/(?<=\s|^)(true|false)(?=\s|$)/g},{type:"oper",match:/[=(){}<>!]+/g},{type:"var",match:/(?<=\s|^)[\w_]+(?=\s*=)/g},c];
