var c=Object.defineProperty;var p=Object.getOwnPropertyDescriptor;var g=Object.getOwnPropertyNames;var h=Object.prototype.hasOwnProperty;var y=(e,t)=>{for(var a in t)c(e,a,{get:t[a],enumerable:!0})},i=(e,t,a,d)=>{if(t&&typeof t=="object"||typeof t=="function")for(let m of g(t))!h.call(e,m)&&m!==a&&c(e,m,{get:()=>t[m],enumerable:!(d=p(t,m))||d.enumerable});return e};var l=e=>i(c({},"__esModule",{value:!0}),e);var o={};y(o,{default:()=>n});module.exports=l(o);var n=[{type:"deleted",match:/^[-<].*/gm},{type:"insert",match:/^[+>].*/gm},{type:"kwd",match:/!.*/gm},{type:"section",match:/^@@.*@@$|^\d.*|^([*-+])\1\1.*/gm}];
