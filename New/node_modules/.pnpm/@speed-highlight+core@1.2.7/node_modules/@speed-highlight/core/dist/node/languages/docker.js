var r=Object.defineProperty;var o=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var i=Object.prototype.hasOwnProperty;var d=(t,e)=>{for(var s in e)r(t,s,{get:e[s],enumerable:!0})},h=(t,e,s,l)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of m(e))!i.call(t,a)&&a!==s&&r(t,a,{get:()=>e[a],enumerable:!(l=o(e,a))||l.enumerable});return t};var n=t=>h(r({},"__esModule",{value:!0}),t);var y={};d(y,{default:()=>u});module.exports=n(y);var p={type:"var",match:/\$\w+|\${[^}]*}|\$\([^)]*\)/g},c=[{sub:"todo",match:/#.*/g},{type:"str",match:/(["'])((?!\1)[^\r\n\\]|\\[^])*\1?/g,sub:[p]},{type:"oper",match:/(?<=\s|^)\.*\/[a-z/_.-]+/gi},{type:"kwd",match:/\s-[a-zA-Z]+|$<|[&|;]+|\b(unset|readonly|shift|export|if|fi|else|elif|while|do|done|for|until|case|esac|break|continue|exit|return|trap|wait|eval|exec|then|declare|enable|local|select|typeset|time|add|remove|install|update|delete)(?=\s|$)/g},{expand:"num"},{type:"func",match:/(?<=(^|\||\&\&|\;)\s*)[a-z_.-]+(?=\s|$)/gmi},{type:"bool",match:/(?<=\s|^)(true|false)(?=\s|$)/g},{type:"oper",match:/[=(){}<>!]+/g},{type:"var",match:/(?<=\s|^)[\w_]+(?=\s*=)/g},p];var u=[{type:"kwd",match:/^(FROM|RUN|CMD|LABEL|MAINTAINER|EXPOSE|ENV|ADD|COPY|ENTRYPOINT|VOLUME|USER|WORKDIR|ARG|ONBUILD|STOPSIGNAL|HEALTHCHECK|SHELL)\b/gmi},...c];
