declare const _default: ({
    match: RegExp;
    sub: ({
        type: string;
        match: RegExp;
        sub: {
            type: string;
            match: RegExp;
        }[];
    } | {
        type: string;
        match: RegExp;
        sub?: undefined;
    })[];
} | {
    match: RegExp;
    sub: string;
    type?: undefined;
} | {
    type: string;
    match: RegExp;
    sub?: undefined;
} | {
    match: RegExp;
    sub: ({
        match: RegExp;
        sub: ({
            type: string;
            match: RegExp;
            sub: {
                type: string;
                match: RegExp;
            }[];
        } | {
            type: string;
            match: RegExp;
            sub?: undefined;
        })[];
    } | {
        match: RegExp;
        sub: string;
    })[];
})[];
export default _default;
//# sourceMappingURL=html.d.ts.map