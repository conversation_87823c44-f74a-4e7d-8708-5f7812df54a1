var b=Object.defineProperty;var m=Object.getOwnPropertyDescriptor;var n=Object.getOwnPropertyNames;var u=Object.prototype.hasOwnProperty;var r=(a,t)=>{for(var p in t)b(a,p,{get:t[p],enumerable:!0})},c=(a,t,p,l)=>{if(t&&typeof t=="object"||typeof t=="function")for(let e of n(t))!u.call(a,e)&&e!==p&&b(a,e,{get:()=>t[e],enumerable:!(l=m(t,e))||l.enumerable});return a};var d=a=>c(b({},"__esModule",{value:!0}),a);var h={};r(h,{default:()=>g});module.exports=d(h);var g=[{type:"var",match:/("|')?[a-zA-Z]\w*\1(?=\s*:)/g},{expand:"str"},{expand:"num"},{type:"num",match:/\bnull\b/g},{type:"bool",match:/\b(true|false)\b/g}];
