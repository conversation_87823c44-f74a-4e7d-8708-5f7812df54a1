var r=Object.defineProperty;var l=Object.getOwnPropertyDescriptor;var p=Object.getOwnPropertyNames;var i=Object.prototype.hasOwnProperty;var m=(t,e)=>{for(var n in e)r(t,n,{get:e[n],enumerable:!0})},b=(t,e,n,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of p(e))!i.call(t,a)&&a!==n&&r(t,a,{get:()=>e[a],enumerable:!(o=l(e,a))||o.enumerable});return t};var d=t=>b(r({},"__esModule",{value:!0}),t);var c={};m(c,{default:()=>u});module.exports=d(c);var u=[{type:"cmnt",match:/^#.*/gm},{expand:"strDouble"},{expand:"num"},{type:"err",match:/\b(err(or)?|[a-z_-]*exception|warn|warning|failed|ko|invalid|not ?found|alert|fatal)\b/gi},{type:"num",match:/\b(null|undefined)\b/gi},{type:"bool",match:/\b(false|true|yes|no)\b/gi},{type:"oper",match:/\.|,/g}];
