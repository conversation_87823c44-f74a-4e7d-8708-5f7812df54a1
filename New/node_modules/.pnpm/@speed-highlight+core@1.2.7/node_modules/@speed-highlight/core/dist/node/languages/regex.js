var m=Object.defineProperty;var o=Object.getOwnPropertyDescriptor;var r=Object.getOwnPropertyNames;var c=Object.prototype.hasOwnProperty;var g=(e,t)=>{for(var a in t)m(e,a,{get:t[a],enumerable:!0})},h=(e,t,a,d)=>{if(t&&typeof t=="object"||typeof t=="function")for(let p of r(t))!c.call(e,p)&&p!==a&&m(e,p,{get:()=>t[p],enumerable:!(d=o(t,p))||d.enumerable});return e};var y=e=>h(m({},"__esModule",{value:!0}),e);var n={};g(n,{default:()=>u,type:()=>l});module.exports=y(n);var u=[{match:/^(?!\/).*/gm,sub:"todo"},{type:"num",match:/\[((?!\])[^\\]|\\.)*\]/g},{type:"kwd",match:/\||\^|\$|\\.|\w+($|\r|\n)/g},{type:"var",match:/\*|\+|\{\d+,\d+\}/g}],l="oper";0&&(module.exports={type});
