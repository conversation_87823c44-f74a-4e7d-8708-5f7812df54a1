var p=Object.defineProperty;var n=Object.getOwnPropertyDescriptor;var g=Object.getOwnPropertyNames;var h=Object.prototype.hasOwnProperty;var o=(e,t)=>{for(var m in t)p(e,m,{get:t[m],enumerable:!0})},b=(e,t,m,c)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of g(t))!h.call(e,a)&&a!==m&&p(e,a,{get:()=>t[a],enumerable:!(c=n(t,a))||c.enumerable});return e};var d=e=>b(p({},"__esModule",{value:!0}),e);var s={};o(s,{default:()=>r});module.exports=d(s);var r=[{match:/#.*/g,sub:"todo"},{type:"str",match:/("""|''')((?!\1)[^]|\\[^])*\1?/g},{expand:"str"},{type:"section",match:/^\[.+\]\s*$/gm},{type:"num",match:/\b(inf|nan)\b|\d[\d:ZT.-]*/g},{expand:"num"},{type:"bool",match:/\b(true|false)\b/g},{type:"oper",match:/[+,.=-]/g},{type:"var",match:/\w+(?= \=)/g}];
