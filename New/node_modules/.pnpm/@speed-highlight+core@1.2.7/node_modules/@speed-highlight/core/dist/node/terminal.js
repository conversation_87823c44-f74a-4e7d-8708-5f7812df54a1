var N=Object.defineProperty;var pe=Object.getOwnPropertyDescriptor;var ne=Object.getOwnPropertyNames;var se=Object.prototype.hasOwnProperty;var b=e=>t=>{var r=e[t];if(r)return r();throw new Error("Module not found in bundle: "+t)};var a=(e,t)=>()=>(e&&(t=e(e=0)),t);var p=(e,t)=>{for(var r in t)N(e,r,{get:t[r],enumerable:!0})},re=(e,t,r,m)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of ne(t))!se.call(e,s)&&s!==r&&N(e,s,{get:()=>t[s],enumerable:!(m=pe(t,s))||m.enumerable});return e};var ce=e=>re(N({},"__esModule",{value:!0}),e);var k={};p(k,{default:()=>me});var me,H=a(()=>{me=[{type:"cmnt",match:/(;|#).*/gm},{expand:"str"},{expand:"num"},{type:"num",match:/\$[\da-fA-F]*\b/g},{type:"kwd",match:/^[a-z]+\s+[a-z.]+\b/gm,sub:[{type:"func",match:/^[a-z]+/g}]},{type:"kwd",match:/^\t*[a-z][a-z\d]*\b/gm},{match:/%|\$/g,type:"oper"}]});var _={};p(_,{default:()=>A});var z,A,R=a(()=>{z={type:"var",match:/\$\w+|\${[^}]*}|\$\([^)]*\)/g},A=[{sub:"todo",match:/#.*/g},{type:"str",match:/(["'])((?!\1)[^\r\n\\]|\\[^])*\1?/g,sub:[z]},{type:"oper",match:/(?<=\s|^)\.*\/[a-z/_.-]+/gi},{type:"kwd",match:/\s-[a-zA-Z]+|$<|[&|;]+|\b(unset|readonly|shift|export|if|fi|else|elif|while|do|done|for|until|case|esac|break|continue|exit|return|trap|wait|eval|exec|then|declare|enable|local|select|typeset|time|add|remove|install|update|delete)(?=\s|$)/g},{expand:"num"},{type:"func",match:/(?<=(^|\||\&\&|\;)\s*)[a-z_.-]+(?=\s|$)/gmi},{type:"bool",match:/(?<=\s|^)(true|false)(?=\s|$)/g},{type:"oper",match:/[=(){}<>!]+/g},{type:"var",match:/(?<=\s|^)[\w_]+(?=\s*=)/g},z]});var Y={};p(Y,{default:()=>oe});var oe,Z=a(()=>{oe=[{match:/[^\[\->+.<\]\s].*/g,sub:"todo"},{type:"func",match:/\.+/g},{type:"kwd",match:/[<>]+/g},{type:"oper",match:/[+-]+/g}]});var X={};p(X,{default:()=>le});var le,W=a(()=>{le=[{match:/\/\/.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{expand:"num"},{type:"kwd",match:/#\s*include (<.*>|".*")/g,sub:[{type:"str",match:/(<|").*/g}]},{match:/asm\s*{[^}]*}/g,sub:[{type:"kwd",match:/^asm/g},{match:/[^{}]*(?=}$)/g,sub:"asm"}]},{type:"kwd",match:/\*|&|#[a-z]+\b|\b(asm|auto|double|int|struct|break|else|long|switch|case|enum|register|typedef|char|extern|return|union|const|float|short|unsigned|continue|for|signed|void|default|goto|sizeof|volatile|do|if|static|while)\b/g},{type:"oper",match:/[/*+:?&|%^~=!,<>.^-]+/g},{type:"func",match:/[a-zA-Z_][\w_]*(?=\s*\()/g},{type:"class",match:/\b[A-Z][\w_]*\b/g}]});var j={};p(j,{default:()=>ue});var ue,K=a(()=>{ue=[{match:/\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{type:"kwd",match:/@\w+\b|\b(and|not|only|or)\b|\b[a-z-]+(?=[^{}]*{)/g},{type:"var",match:/\b[\w-]+(?=\s*:)|(::?|\.)[\w-]+(?=[^{}]*{)/g},{type:"func",match:/#[\w-]+(?=[^{}]*{)/g},{type:"num",match:/#[\da-f]{3,8}/g},{type:"num",match:/\d+(\.\d+)?(cm|mm|in|px|pt|pc|em|ex|ch|rem|vm|vh|vmin|vmax|%)?/g,sub:[{type:"var",match:/[a-z]+|%/g}]},{match:/url\([^)]*\)/g,sub:[{type:"func",match:/url(?=\()/g},{type:"str",match:/[^()]+/g}]},{type:"func",match:/\b[a-zA-Z]\w*(?=\s*\()/g},{type:"num",match:/\b[a-z-]+\b/g}]});var V={};p(V,{default:()=>Ee});var Ee,q=a(()=>{Ee=[{expand:"strDouble"},{type:"oper",match:/,/g}]});var Q={};p(Q,{default:()=>O});var O,x=a(()=>{O=[{type:"deleted",match:/^[-<].*/gm},{type:"insert",match:/^[+>].*/gm},{type:"kwd",match:/!.*/gm},{type:"section",match:/^@@.*@@$|^\d.*|^([*-+])\1\1.*/gm}]});var J={};p(J,{default:()=>ie});var ie,tt=a(()=>{R();ie=[{type:"kwd",match:/^(FROM|RUN|CMD|LABEL|MAINTAINER|EXPOSE|ENV|ADD|COPY|ENTRYPOINT|VOLUME|USER|WORKDIR|ARG|ONBUILD|STOPSIGNAL|HEALTHCHECK|SHELL)\b/gmi},...A]});var et={};p(et,{default:()=>he});var he,at=a(()=>{x();he=[{match:/^#.*/gm,sub:"todo"},{expand:"str"},...O,{type:"func",match:/^(\$ )?git(\s.*)?$/gm},{type:"kwd",match:/^commit \w+$/gm}]});var pt={};p(pt,{default:()=>ge});var ge,nt=a(()=>{ge=[{match:/\/\/.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{expand:"num"},{type:"kwd",match:/\*|&|\b(break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go|goto|if|import|interface|map|package|range|return|select|struct|switch|type|var)\b/g},{type:"func",match:/[a-zA-Z_][\w_]*(?=\s*\()/g},{type:"class",match:/\b[A-Z][\w_]*\b/g},{type:"oper",match:/[+\-*\/%&|^~=!<>.^-]+/g}]});var rt={};p(rt,{default:()=>L,name:()=>E,properties:()=>i,xmlElement:()=>u});var st,de,E,i,u,L,S=a(()=>{st=":A-Z_a-z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD",de=st+"\\-\\.0-9\xB7\u0300-\u036F\u203F-\u2040",E=`[${st}][${de}]*`,i=`\\s*(\\s+${E}\\s*(=\\s*([^"']\\S*|("|')(\\\\[^]|(?!\\4)[^])*\\4?)?)?\\s*)*`,u={match:RegExp(`<[/!?]?${E}${i}[/!?]?>`,"g"),sub:[{type:"var",match:RegExp(`^<[/!?]?${E}`,"g"),sub:[{type:"oper",match:/^<[\/!?]?/g}]},{type:"str",match:/=\s*([^"']\S*|("|')(\\[^]|(?!\2)[^])*\2?)/g,sub:[{type:"oper",match:/^=/g}]},{type:"oper",match:/[\/!?]?>/g},{type:"class",match:RegExp(E,"g")}]},L=[{match:/<!--((?!-->)[^])*-->/g,sub:"todo"},{type:"class",match:/<!\[CDATA\[[\s\S]*?\]\]>/gi},u,{type:"str",match:RegExp(`<\\?${E}([^?]|\\?[^?>])*\\?+>`,"g"),sub:[{type:"var",match:RegExp(`^<\\?${E}`,"g"),sub:[{type:"oper",match:/^<\?/g}]},{type:"oper",match:/\?+>$/g}]},{type:"var",match:/&(#x?)?[\da-z]{1,8};/gi}]});var ct={};p(ct,{default:()=>be});var be,mt=a(()=>{S();be=[{type:"class",match:/<!DOCTYPE("[^"]*"|'[^']*'|[^"'>])*>/gi,sub:[{type:"str",match:/"[^"]*"|'[^']*'/g},{type:"oper",match:/^<!|>$/g},{type:"var",match:/DOCTYPE/gi}]},{match:RegExp(`<style${i}>((?!</style>)[^])*</style\\s*>`,"g"),sub:[{match:RegExp(`^<style${i}>`,"g"),sub:u.sub},{match:RegExp(`${u.match}|[^]*(?=</style\\s*>$)`,"g"),sub:"css"},u]},{match:RegExp(`<script${i}>((?!</script>)[^])*</script\\s*>`,"g"),sub:[{match:RegExp(`^<script${i}>`,"g"),sub:u.sub},{match:RegExp(`${u.match}|[^]*(?=</script\\s*>$)`,"g"),sub:"js"},u]},...L]});var ye,h,y=a(()=>{ye=[["bash",[/#!(\/usr)?\/bin\/bash/g,500],[/\b(if|elif|then|fi|echo)\b|\$/g,10]],["html",[/<\/?[a-z-]+[^\n>]*>/g,10],[/^\s+<!DOCTYPE\s+html/g,500]],["http",[/^(GET|HEAD|POST|PUT|DELETE|PATCH|HTTP)\b/g,500]],["js",[/\b(console|await|async|function|export|import|this|class|for|let|const|map|join|require)\b/g,10]],["ts",[/\b(console|await|async|function|export|import|this|class|for|let|const|map|join|require|implements|interface|namespace)\b/g,10]],["py",[/\b(def|print|class|and|or|lambda)\b/g,10]],["sql",[/\b(SELECT|INSERT|FROM)\b/g,50]],["pl",[/#!(\/usr)?\/bin\/perl/g,500],[/\b(use|print)\b|\$/g,10]],["lua",[/#!(\/usr)?\/bin\/lua/g,500]],["make",[/\b(ifneq|endif|if|elif|then|fi|echo|.PHONY|^[a-z]+ ?:$)\b|\$/gm,10]],["uri",[/https?:|mailto:|tel:|ftp:/g,30]],["css",[/^(@import|@page|@media|(\.|#)[a-z]+)/gm,20]],["diff",[/^[+><-]/gm,10],[/^@@ ?[-+,0-9 ]+ ?@@/gm,25]],["md",[/^(>|\t\*|\t\d+.)/gm,10],[/\[.*\](.*)/g,10]],["docker",[/^(FROM|ENTRYPOINT|RUN)/gm,500]],["xml",[/<\/?[a-z-]+[^\n>]*>/g,10],[/^<\?xml/g,500]],["c",[/#include\b|\bprintf\s+\(/g,100]],["rs",[/^\s+(use|fn|mut|match)\b/gm,100]],["go",[/\b(func|fmt|package)\b/g,100]],["java",[/^import\s+java/gm,500]],["asm",[/^(section|global main|extern|\t(call|mov|ret))/gm,100]],["css",[/^(@import|@page|@media|(\.|#)[a-z]+)/gm,20]],["json",[/\b(true|false|null|\{})\b|\"[^"]+\":/g,10]],["yaml",[/^(\s+)?[a-z][a-z0-9]*:/gmi,10]]],h=e=>{var t;return((t=ye.map(([r,...m])=>[r,m.reduce((s,[o,c])=>s+[...e.matchAll(o)].length*c,0)]).filter(([r,m])=>m>20).sort((r,m)=>m[1]-r[1])[0])==null?void 0:t[0])||"plain"}});var ot={};p(ot,{default:()=>Te});var Te,lt=a(()=>{y();Te=[{type:"kwd",match:/^(GET|HEAD|POST|PUT|DELETE|CONNECT|OPTIONS|TRACE|PATCH|PRI|SEARCH)\b/gm},{expand:"str"},{type:"section",match:/\bHTTP\/[\d.]+\b/g},{expand:"num"},{type:"oper",match:/[,;:=]/g},{type:"var",match:/[a-zA-Z][\w-]*(?=:)/g},{match:/\n\n[^]*/g,sub:h}]});var ut={};p(ut,{default:()=>fe});var fe,Et=a(()=>{fe=[{match:/(^[ \f\t\v]*)[#;].*/gm,sub:"todo"},{type:"str",match:/.*/g},{type:"var",match:/.*(?==)/g},{type:"section",match:/^\s*\[.+\]\s*$/gm},{type:"oper",match:/=/g}]});var it={};p(it,{default:()=>Ie});var Ie,ht=a(()=>{Ie=[{match:/\/\/.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{expand:"num"},{type:"kwd",match:/\b(abstract|assert|boolean|break|byte|case|catch|char|class|continue|const|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|package|private|protected|public|requires|return|short|static|strictfp|super|switch|synchronized|this|throw|throws|transient|try|var|void|volatile|while)\b/g},{type:"oper",match:/[/*+:?&|%^~=!,<>.^-]+/g},{type:"func",match:/[a-zA-Z_][\w_]*(?=\s*\()/g},{type:"class",match:/\b[A-Z][\w_]*\b/g}]});var gt={};p(gt,{default:()=>C});var C,D=a(()=>{C=[{match:/\/\*\*((?!\*\/)[^])*(\*\/)?/g,sub:"jsdoc"},{match:/\/\/.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{match:/`((?!`)[^]|\\[^])*`?/g,sub:"js_template_literals"},{type:"kwd",match:/=>|\b(this|set|get|as|async|await|break|case|catch|class|const|constructor|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|if|implements|import|in|instanceof|interface|let|var|of|new|package|private|protected|public|return|static|super|switch|throw|throws|try|typeof|void|while|with|yield)\b/g},{match:/\/((?!\/)[^\r\n\\]|\\.)+\/[dgimsuy]*/g,sub:"regex"},{expand:"num"},{type:"num",match:/\b(NaN|null|undefined|[A-Z][A-Z_]*)\b/g},{type:"bool",match:/\b(true|false)\b/g},{type:"oper",match:/[/*+:?&|%^~=!,<>.^-]+/g},{type:"class",match:/\b[A-Z][\w_]*\b/g},{type:"func",match:/[a-zA-Z$_][\w$_]*(?=\s*((\?\.)?\s*\(|=\s*(\(?[\w,{}\[\])]+\)? =>|function\b)))/g}]});var dt={};p(dt,{default:()=>Ne,type:()=>Ae});var Ne,Ae,bt=a(()=>{Ne=[{match:new class{exec(e){let t=this.lastIndex,r,m=s=>{for(;++t<e.length-2;)if(e[t]=="{")m();else if(e[t]=="}")return};for(;t<e.length;++t)if(e[t-1]!="\\"&&e[t]=="$"&&e[t+1]=="{")return r=t++,m(t),this.lastIndex=t+1,{index:r,0:e.slice(r,t+1)};return null}},sub:[{type:"kwd",match:/^\${|}$/g},{match:/(?!^\$|{)[^]+(?=}$)/g,sub:"js"}]}],Ae="str"});var yt={};p(yt,{default:()=>w,type:()=>Re});var w,Re,U=a(()=>{w=[{type:"err",match:/\b(TODO|FIXME|DEBUG|OPTIMIZE|WARNING|XXX|BUG)\b/g},{type:"class",match:/\bIDEA\b/g},{type:"insert",match:/\b(CHANGED|FIX|CHANGE)\b/g},{type:"oper",match:/\bQUESTION\b/g}],Re="cmnt"});var Tt={};p(Tt,{default:()=>Oe,type:()=>xe});var Oe,xe,ft=a(()=>{U();Oe=[{type:"kwd",match:/@\w+/g},{type:"class",match:/{[\w\s|<>,.@\[\]]+}/g},{type:"var",match:/\[[\w\s="']+\]/g},...w],xe="cmnt"});var It={};p(It,{default:()=>Le});var Le,Nt=a(()=>{Le=[{type:"var",match:/("|')?[a-zA-Z]\w*\1(?=\s*:)/g},{expand:"str"},{expand:"num"},{type:"num",match:/\bnull\b/g},{type:"bool",match:/\b(true|false)\b/g}]});var At={};p(At,{default:()=>P});var P,F=a(()=>{y();P=[{type:"cmnt",match:/^>.*|(=|-)\1+/gm},{type:"class",match:/\*\*((?!\*\*).)*\*\*/g},{match:/```((?!```)[^])*\n```/g,sub:e=>({type:"kwd",sub:[{match:/\n[^]*(?=```)/g,sub:e.split(`
`)[0].slice(3)||h(e)}]})},{type:"str",match:/`[^`]*`/g},{type:"var",match:/~~((?!~~).)*~~/g},{type:"kwd",match:/_[^_]*_|\*[^*]*\*/g},{type:"kwd",match:/^\s*(\*|\d+\.)\s/gm},{type:"oper",match:/\[[^\]]*]/g},{type:"func",match:/\([^)]*\)/g}]});var Rt={};p(Rt,{default:()=>Se});var Se,Ot=a(()=>{F();y();Se=[{type:"insert",match:/(leanpub-start-insert)((?!leanpub-end-insert)[^])*(leanpub-end-insert)?/g,sub:[{type:"insert",match:/leanpub-(start|end)-insert/g},{match:/(?!leanpub-start-insert)((?!leanpub-end-insert)[^])*/g,sub:h}]},{type:"deleted",match:/(leanpub-start-delete)((?!leanpub-end-delete)[^])*(leanpub-end-delete)?/g,sub:[{type:"deleted",match:/leanpub-(start|end)-delete/g},{match:/(?!leanpub-start-delete)((?!leanpub-end-delete)[^])*/g,sub:h}]},...P]});var xt={};p(xt,{default:()=>Ce});var Ce,Lt=a(()=>{Ce=[{type:"cmnt",match:/^#.*/gm},{expand:"strDouble"},{expand:"num"},{type:"err",match:/\b(err(or)?|[a-z_-]*exception|warn|warning|failed|ko|invalid|not ?found|alert|fatal)\b/gi},{type:"num",match:/\b(null|undefined)\b/gi},{type:"bool",match:/\b(false|true|yes|no)\b/gi},{type:"oper",match:/\.|,/g}]});var St={};p(St,{default:()=>De});var De,Ct=a(()=>{De=[{match:/^#!.*|--(\[(=*)\[((?!--\]\2\])[^])*--\]\2\]|.*)/g,sub:"todo"},{expand:"str"},{type:"kwd",match:/\b(and|break|do|else|elseif|end|for|function|if|in|local|not|or|repeat|return|then|until|while)\b/g},{type:"bool",match:/\b(true|false|nil)\b/g},{type:"oper",match:/[+*/%^#=~<>:,.-]+/g},{expand:"num"},{type:"func",match:/[a-z_]+(?=\s*[({])/g}]});var Dt={};p(Dt,{default:()=>we});var we,wt=a(()=>{we=[{match:/^\s*#.*/gm,sub:"todo"},{expand:"str"},{type:"oper",match:/[${}()]+/g},{type:"class",match:/.PHONY:/gm},{type:"section",match:/^[\w.]+:/gm},{type:"kwd",match:/\b(ifneq|endif)\b/g},{expand:"num"},{type:"var",match:/[A-Z_]+(?=\s*=)/g},{match:/^.*$/gm,sub:"bash"}]});var Ut={};p(Ut,{default:()=>Ue});var Ue,Pt=a(()=>{Ue=[{match:/#.*/g,sub:"todo"},{type:"str",match:/(["'])(\\[^]|(?!\1)[^])*\1?/g},{expand:"num"},{type:"kwd",match:/\b(any|break|continue|default|delete|die|do|else|elsif|eval|for|foreach|given|goto|if|last|local|my|next|our|package|print|redo|require|return|say|state|sub|switch|undef|unless|until|use|when|while|not|and|or|xor)\b/g},{type:"oper",match:/[-+*/%~!&<>|=?,]+/g},{type:"func",match:/[a-z_]+(?=\s*\()/g}]});var Ft={};p(Ft,{default:()=>Pe});var Pe,Mt=a(()=>{Pe=[{expand:"strDouble"}]});var $t={};p($t,{default:()=>Fe});var Fe,vt=a(()=>{Fe=[{match:/#.*/g,sub:"todo"},{match:/("""|''')(\\[^]|(?!\1)[^])*\1?/g,sub:"todo"},{type:"str",match:/f("|')(\\[^]|(?!\1).)*\1?|f((["'])\4\4)(\\[^]|(?!\3)[^])*\3?/gi,sub:[{type:"var",match:/{[^{}]*}/g,sub:[{match:/(?!^{)[^]*(?=}$)/g,sub:"py"}]}]},{expand:"str"},{type:"kwd",match:/\b(and|as|assert|break|class|continue|def|del|elif|else|except|finally|for|from|global|if|import|in|is|lambda|nonlocal|not|or|pass|raise|return|try|while|with|yield)\b/g},{type:"bool",match:/\b(False|True|None)\b/g},{expand:"num"},{type:"func",match:/[a-z_]+(?=\s*\()/g},{type:"oper",match:/[-/*+<>,=!&|^%]+/g},{type:"class",match:/\b[A-Z][\w_]*\b/g}]});var Bt={};p(Bt,{default:()=>Me,type:()=>$e});var Me,$e,Gt=a(()=>{Me=[{match:/^(?!\/).*/gm,sub:"todo"},{type:"num",match:/\[((?!\])[^\\]|\\.)*\]/g},{type:"kwd",match:/\||\^|\$|\\.|\w+($|\r|\n)/g},{type:"var",match:/\*|\+|\{\d+,\d+\}/g}],$e="oper"});var kt={};p(kt,{default:()=>ve});var ve,Ht=a(()=>{ve=[{match:/\/\/.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{expand:"num"},{type:"kwd",match:/\b(as|break|const|continue|crate|else|enum|extern|false|fn|for|if|impl|in|let|loop|match|mod|move|mut|pub|ref|return|self|Self|static|struct|super|trait|true|type|unsafe|use|where|while|async|await|dyn|abstract|become|box|do|final|macro|override|priv|typeof|unsized|virtual|yield|try)\b/g},{type:"oper",match:/[/*+:?&|%^~=!,<>.^-]+/g},{type:"class",match:/\b[A-Z][\w_]*\b/g},{type:"func",match:/[a-zA-Z_][\w_]*(?=\s*!?\s*\()/g}]});var zt={};p(zt,{default:()=>Be});var Be,_t=a(()=>{Be=[{match:/--.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{type:"func",match:/\b(AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/g},{type:"kwd",match:/\b(ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:_INSERT|COL)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|kwdS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:S|ING)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/g},{type:"num",match:/\.?\d[\d.oxa-fA-F-]*|\bNULL\b/g},{type:"bool",match:/\b(TRUE|FALSE)\b/g},{type:"oper",match:/[-+*\/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|IN|ILIKE|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/g},{type:"var",match:/@\S+/g}]});var Yt={};p(Yt,{default:()=>Ge});var Ge,Zt=a(()=>{Ge=[{match:/#.*/g,sub:"todo"},{type:"str",match:/("""|''')((?!\1)[^]|\\[^])*\1?/g},{expand:"str"},{type:"section",match:/^\[.+\]\s*$/gm},{type:"num",match:/\b(inf|nan)\b|\d[\d:ZT.-]*/g},{expand:"num"},{type:"bool",match:/\b(true|false)\b/g},{type:"oper",match:/[+,.=-]/g},{type:"var",match:/\w+(?= \=)/g}]});var Xt={};p(Xt,{default:()=>ke});var ke,Wt=a(()=>{D();ke=[{type:"type",match:/:\s*(any|void|number|boolean|string|object|never|enum)\b/g},{type:"kwd",match:/\b(type|namespace|typedef|interface|public|private|protected|implements|declare|abstract|readonly)\b/g},...C]});var jt={};p(jt,{default:()=>He});var He,Kt=a(()=>{He=[{match:/^#.*/gm,sub:"todo"},{type:"class",match:/^\w+(?=:?)/gm},{type:"num",match:/:\d+/g},{type:"oper",match:/[:/&?]|\w+=/g},{type:"func",match:/[.\w]+@|#[\w]+$/gm},{type:"var",match:/\w+\.\w+(\.\w+)*/g}]});var Vt={};p(Vt,{default:()=>ze});var ze,qt=a(()=>{ze=[{match:/#.*/g,sub:"todo"},{expand:"str"},{type:"str",match:/(>|\|)\r?\n((\s[^\n]*)?(\r?\n|$))*/g},{type:"type",match:/!![a-z]+/g},{type:"bool",match:/\b(Yes|No)\b/g},{type:"oper",match:/[+:-]/g},{expand:"num"},{type:"var",match:/[a-zA-Z]\w*(?=:)/g}]});var Qt={};p(Qt,{default:()=>n});var n,T=a(()=>{n={black:"\x1B[30m",red:"\x1B[31m",green:"\x1B[32m",gray:"\x1B[90m",yellow:"\x1B[33m",blue:"\x1B[34m",magenta:"\x1B[35m",cyan:"\x1B[36m",white:"\x1B[37m"}});var Jt={};p(Jt,{default:()=>Ye});var Ye,te=a(()=>{T();Ye={deleted:n.red,var:n.red,err:n.red,kwd:n.magenta,num:n.yellow,class:n.yellow,cmnt:n.gray,insert:n.green,str:n.green,bool:n.cyan,type:n.blue,oper:n.blue,section:n.magenta,func:n.blue}});var v={};p(v,{default:()=>Ze});var Ze,B=a(()=>{T();Ze={deleted:n.red,var:n.red,err:n.red,kwd:n.red,num:n.yellow,class:n.yellow,cmnt:n.gray,insert:n.green,str:n.green,bool:n.cyan,type:n.blue,oper:n.blue,section:n.magenta,func:n.magenta}});var Ke={};p(Ke,{highlightText:()=>ae,printHighlight:()=>We,setTheme:()=>je});module.exports=ce(Ke);var G={num:{type:"num",match:/(\.e?|\b)\d(e-|[\d.oxa-fA-F_])*(\.|\b)/g},str:{type:"str",match:/(["'])(\\[^]|(?!\1)[^\r\n\\])*\1?/g},strDouble:{type:"str",match:/"((?!")[^\r\n\\]|\\[^])*"?/g}};var _e=b({"./languages/asm.js":()=>Promise.resolve().then(()=>(H(),k)),"./languages/bash.js":()=>Promise.resolve().then(()=>(R(),_)),"./languages/bf.js":()=>Promise.resolve().then(()=>(Z(),Y)),"./languages/c.js":()=>Promise.resolve().then(()=>(W(),X)),"./languages/css.js":()=>Promise.resolve().then(()=>(K(),j)),"./languages/csv.js":()=>Promise.resolve().then(()=>(q(),V)),"./languages/diff.js":()=>Promise.resolve().then(()=>(x(),Q)),"./languages/docker.js":()=>Promise.resolve().then(()=>(tt(),J)),"./languages/git.js":()=>Promise.resolve().then(()=>(at(),et)),"./languages/go.js":()=>Promise.resolve().then(()=>(nt(),pt)),"./languages/html.js":()=>Promise.resolve().then(()=>(mt(),ct)),"./languages/http.js":()=>Promise.resolve().then(()=>(lt(),ot)),"./languages/ini.js":()=>Promise.resolve().then(()=>(Et(),ut)),"./languages/java.js":()=>Promise.resolve().then(()=>(ht(),it)),"./languages/js.js":()=>Promise.resolve().then(()=>(D(),gt)),"./languages/js_template_literals.js":()=>Promise.resolve().then(()=>(bt(),dt)),"./languages/jsdoc.js":()=>Promise.resolve().then(()=>(ft(),Tt)),"./languages/json.js":()=>Promise.resolve().then(()=>(Nt(),It)),"./languages/leanpub-md.js":()=>Promise.resolve().then(()=>(Ot(),Rt)),"./languages/log.js":()=>Promise.resolve().then(()=>(Lt(),xt)),"./languages/lua.js":()=>Promise.resolve().then(()=>(Ct(),St)),"./languages/make.js":()=>Promise.resolve().then(()=>(wt(),Dt)),"./languages/md.js":()=>Promise.resolve().then(()=>(F(),At)),"./languages/pl.js":()=>Promise.resolve().then(()=>(Pt(),Ut)),"./languages/plain.js":()=>Promise.resolve().then(()=>(Mt(),Ft)),"./languages/py.js":()=>Promise.resolve().then(()=>(vt(),$t)),"./languages/regex.js":()=>Promise.resolve().then(()=>(Gt(),Bt)),"./languages/rs.js":()=>Promise.resolve().then(()=>(Ht(),kt)),"./languages/sql.js":()=>Promise.resolve().then(()=>(_t(),zt)),"./languages/todo.js":()=>Promise.resolve().then(()=>(U(),yt)),"./languages/toml.js":()=>Promise.resolve().then(()=>(Zt(),Yt)),"./languages/ts.js":()=>Promise.resolve().then(()=>(Wt(),Xt)),"./languages/uri.js":()=>Promise.resolve().then(()=>(Kt(),jt)),"./languages/xml.js":()=>Promise.resolve().then(()=>(S(),rt)),"./languages/yaml.js":()=>Promise.resolve().then(()=>(qt(),Vt))});var M={};async function $(e,t,r){var m;try{let s,o,c={},f,l=[],g=0,I=typeof t=="string"?await((m=M[t])!=null?m:M[t]=_e(`./languages/${t}.js`)):t,d=[...typeof t=="string"?I.default:t.sub];for(;g<e.length;){for(c.index=null,s=d.length;s-- >0;){if(o=d[s].expand?G[d[s].expand]:d[s],l[s]===void 0||l[s].match.index<g){if(o.match.lastIndex=g,f=o.match.exec(e),f===null){d.splice(s,1),l.splice(s,1);continue}l[s]={match:f,lastIndex:o.match.lastIndex}}l[s].match[0]&&(l[s].match.index<=c.index||c.index===null)&&(c={part:o,index:l[s].match.index,match:l[s].match[0],end:l[s].lastIndex})}if(c.index===null)break;r(e.slice(g,c.index),I.type),g=c.end,c.part.sub?await $(c.match,typeof c.part.sub=="string"?c.part.sub:typeof c.part.sub=="function"?c.part.sub(c.match):c.part,r):r(c.match,c.part.type)}r(e.slice(g,e.length),I.type)}catch{r(e)}}var Xe=b({"./themes/atom-dark.js":()=>Promise.resolve().then(()=>(te(),Jt)),"./themes/default.js":()=>Promise.resolve().then(()=>(B(),v)),"./themes/termcolor.js":()=>Promise.resolve().then(()=>(T(),Qt))});var ee=Promise.resolve().then(()=>(B(),v)),ae=async(e,t)=>{let r="",m=(await ee).default;return await $(e,t,(s,o)=>{var c;return r+=o?`${(c=m[o])!=null?c:""}${s}\x1B[0m`:s}),r},We=async(e,t)=>console.log(await ae(e,t)),je=async e=>ee=Xe(`./themes/${e}.js`);0&&(module.exports={highlightText,printHighlight,setTheme});
