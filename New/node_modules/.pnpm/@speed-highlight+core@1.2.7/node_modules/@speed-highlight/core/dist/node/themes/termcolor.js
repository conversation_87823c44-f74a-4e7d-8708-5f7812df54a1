var a=Object.defineProperty;var r=Object.getOwnPropertyDescriptor;var t=Object.getOwnPropertyNames;var g=Object.prototype.hasOwnProperty;var n=(m,b)=>{for(var e in b)a(m,e,{get:b[e],enumerable:!0})},y=(m,b,e,l)=>{if(b&&typeof b=="object"||typeof b=="function")for(let x of t(b))!g.call(m,x)&&x!==e&&a(m,x,{get:()=>b[x],enumerable:!(l=r(b,x))||l.enumerable});return m};var c=m=>y(a({},"__esModule",{value:!0}),m);var o={};n(o,{default:()=>d});module.exports=c(o);var d={black:"\x1B[30m",red:"\x1B[31m",green:"\x1B[32m",gray:"\x1B[90m",yellow:"\x1B[33m",blue:"\x1B[34m",magenta:"\x1B[35m",cyan:"\x1B[36m",white:"\x1B[37m"};
