export function highlightText(src: string, lang: ShjLanguage): Promise<string>;
export function printHighlight(src: string, lang: ShjLanguage): Promise<any>;
export function setTheme(name: ShjTerminalTheme): Promise<any>;
export type ShjLanguage = import("./index.js").ShjLanguage;
/**
 * Languages supported
 */
export type ShjTerminalTheme = ("default" | "atom-dark");
//# sourceMappingURL=terminal.d.ts.map