/**
 * ClinicalCare (CC) API Client
 *
 * Comprehensive API client for ClinicalCare system providing all CRUD operations
 * for patients, appointments, custom fields, users, services, resources, locations,
 * invoices, and payments.
 *
 * Features:
 * - Complete feature parity with v3Integration
 * - TypeScript typing and error handling
 * - Configuration from configs.ts
 * - Data cleaning utilities
 */

import fetch from "node-fetch";
import type {
	GetCCAppointmentCategoryType,
	GetCCAppointmentType,
	GetCCCustomField,
	GetCCLocationType,
	GetCCPatientCustomField,
	GetCCPatientType,
	GetCCResourceType,
	GetCCServiceType,
	GetCCUserType,
	GetInvoiceType,
	GetPaymentType,
	PostCCAppointmentType,
	PostCCPatientType,
	PutCCAppointmentType,
} from "../type/CCTypes";
import cleanData from "../utils/cleanData";
import getConfig from "../utils/configs";

/**
 * HTTP request options interface
 */
interface RequestOptions {
	url: string;
	method: "GET" | "POST" | "PUT" | "DELETE";
	data?: any;
	params?: Record<string, string | number | boolean>;
}

/**
 * Base HTTP client for ClinicalCare API
 */
const ccRequest = async <T = any>(options: RequestOptions): Promise<T> => {
	const { url, method, data, params } = options;

	// Build full URL with base domain
	const baseUrl = getConfig("ccApiDomain");
	let fullUrl = `${baseUrl}${url}`;

	// Add query parameters if provided
	if (params) {
		const searchParams = new URLSearchParams();
		Object.entries(params).forEach(([key, value]) => {
			searchParams.append(key, String(value));
		});
		fullUrl += `?${searchParams.toString()}`;
	}

	// Prepare request headers
	const headers: Record<string, string> = {
		Authorization: `Bearer ${getConfig("ccApiKey")}`,
		Accept: "application/json",
		"Content-Type": "application/json",
	};

	try {
		const response = await fetch(fullUrl, {
			method,
			headers,
			body: data ? JSON.stringify(data) : undefined,
		});

		if (!response.ok) {
			const errorText = await response.text();
			let errorMessage: string;

			try {
				const errorData = JSON.parse(errorText);
				errorMessage =
					errorData.message ||
					errorData.error ||
					`HTTP ${response.status}: ${response.statusText}`;
			} catch {
				errorMessage = `HTTP ${response.status}: ${response.statusText}`;
			}

			throw new Error(`CC API Error: ${errorMessage}`);
		}

		const responseData = await response.json();
		return responseData as T;
	} catch (error) {
		if (error instanceof Error) {
			throw new Error(`CC API Request Failed: ${error.message}`);
		}
		throw new Error("CC API Request Failed: Unknown error");
	}
};

/**
 * Convert array of IDs to query string format
 */
const idsToQueryString = (ids: number[]): string => {
	return ids
		.map((id) => `ids[]=${id.toString().trim()}`)
		.join("&")
		.trim();
};

/**
 * Patient operations
 */
export const patientReq = {
	/**
	 * Create a new patient
	 */
	create: async (data: PostCCPatientType): Promise<GetCCPatientType> => {
		const response = await ccRequest<{ patient: GetCCPatientType }>({
			url: "/patients",
			method: "POST",
			data: {
				patient: cleanData(data),
			},
		});
		return response.patient;
	},

	/**
	 * Update an existing patient
	 */
	update: async (
		id: number,
		data: PostCCPatientType,
	): Promise<GetCCPatientType> => {
		const response = await ccRequest<{ patient: GetCCPatientType }>({
			url: `/patients/${id}`,
			method: "PUT",
			data: {
				patient: cleanData(data),
			},
		});
		return response.patient;
	},

	/**
	 * Get a patient by ID
	 */
	get: async (id: number): Promise<GetCCPatientType> => {
		const response = await ccRequest<{ patient: GetCCPatientType }>({
			url: `/patients/${id}`,
			method: "GET",
		});
		return response.patient;
	},

	/**
	 * Search for a patient by email or phone
	 */
	search: async (emailOrPhone: string): Promise<GetCCPatientType | null> => {
		const response = await ccRequest<{ patients: GetCCPatientType[] }>({
			url: `/patients`,
			method: "GET",
			params: { search: emailOrPhone },
		});
		return response.patients && response.patients.length > 0
			? response.patients[0]
			: null;
	},

	/**
	 * Get all patients with pagination and filtering
	 */
	all: async (
		params: {
			page?: number;
			perPage?: number;
			active?: boolean;
			sort?: string;
		} = {
			page: 1,
			perPage: 20,
			active: true,
			sort: "-createdAt",
		},
	): Promise<GetCCPatientType[]> => {
		const queryParams = {
			[`active${params.active}`]: "",
			"page[number]": params.page || 1,
			"page[size]": params.perPage || 20,
			sort: params.sort || "-createdAt",
		};

		const response = await ccRequest<{ patients: GetCCPatientType[] }>({
			url: "/patients",
			method: "GET",
			params: queryParams,
		});
		return response.patients;
	},

	/**
	 * Get patient custom fields by IDs
	 */
	customFields: async (ids: number[]): Promise<GetCCPatientCustomField[]> => {
		const response = await ccRequest<{
			patientCustomFields: GetCCPatientCustomField[];
		}>({
			url: `/patientCustomFields?${idsToQueryString(ids)}`,
			method: "GET",
		});
		return response.patientCustomFields;
	},
};

/**
 * Custom field operations
 */
export const ccCustomfieldReq = {
	/**
	 * Get a custom field by ID
	 */
	get: async (id: number): Promise<GetCCCustomField> => {
		const response = await ccRequest<{ customField: GetCCCustomField }>({
			url: `/customFields/${id}`,
			method: "GET",
		});
		return response.customField;
	},

	/**
	 * Get all custom fields
	 */
	all: async (): Promise<GetCCCustomField[]> => {
		const response = await ccRequest<{ customFields: GetCCCustomField[] }>({
			url: "/customFields/",
			method: "GET",
		});
		return response.customFields;
	},
};

/**
 * User operations
 */
export const ccUserReq = {
	/**
	 * Get a user by ID
	 */
	get: async (id: number): Promise<GetCCUserType> => {
		const response = await ccRequest<{ user: GetCCUserType }>({
			url: `/users/${id}`,
			method: "GET",
		});
		return response.user;
	},

	/**
	 * Get all users
	 */
	all: async (): Promise<GetCCUserType[]> => {
		const response = await ccRequest<{ users: GetCCUserType[] }>({
			url: "/users",
			method: "GET",
		});
		return response.users;
	},
};

/**
 * Service operations
 */
export const serviceReq = {
	/**
	 * Get all services
	 */
	all: async (): Promise<GetCCServiceType[]> => {
		const response = await ccRequest<{ services: GetCCServiceType[] }>({
			url: "/services",
			method: "GET",
		});
		return response.services;
	},

	/**
	 * Get a service by ID
	 */
	get: async (id: number): Promise<GetCCServiceType> => {
		const response = await ccRequest<{ service: GetCCServiceType }>({
			url: `/services/${id}`,
			method: "GET",
		});
		return response.service;
	},
};

/**
 * Resource operations
 */
export const resourceReq = {
	/**
	 * Get all resources
	 */
	all: async (): Promise<GetCCResourceType[]> => {
		const response = await ccRequest<{ resources: GetCCResourceType[] }>({
			url: "/resources",
			method: "GET",
		});
		return response.resources;
	},

	/**
	 * Get a resource by ID
	 */
	get: async (id: number): Promise<GetCCResourceType> => {
		const response = await ccRequest<{ resource: GetCCResourceType }>({
			url: `/resources/${id}`,
			method: "GET",
		});
		return response.resource;
	},
};

/**
 * Location operations
 */
export const ccLocationReq = {
	/**
	 * Get all locations
	 */
	all: async (): Promise<GetCCLocationType[]> => {
		const response = await ccRequest<{ locations: GetCCLocationType[] }>({
			url: "/locations",
			method: "GET",
		});
		return response.locations;
	},

	/**
	 * Get a location by ID
	 */
	get: async (id: number): Promise<GetCCLocationType> => {
		const response = await ccRequest<{ location: GetCCLocationType }>({
			url: `/locations/${id}`,
			method: "GET",
		});
		return response.location;
	},
};

/**
 * Appointment operations
 */
export const ccAppointmentReq = {
	/**
	 * Get an appointment by ID
	 */
	get: async (id: number): Promise<GetCCAppointmentType> => {
		const response = await ccRequest<{ appointment: GetCCAppointmentType }>({
			url: `/appointments/${id}`,
			method: "GET",
		});
		return response.appointment;
	},

	/**
	 * Create a new appointment
	 */
	post: async (
		payload: PostCCAppointmentType,
	): Promise<GetCCAppointmentType> => {
		const response = await ccRequest<{ appointment: GetCCAppointmentType }>({
			url: "/appointments",
			method: "POST",
			data: { appointment: payload },
		});
		return response.appointment;
	},

	/**
	 * Update an existing appointment
	 */
	put: async (
		id: number,
		payload: PutCCAppointmentType,
	): Promise<GetCCAppointmentType> => {
		const response = await ccRequest<{ appointment: GetCCAppointmentType }>({
			url: `/appointments/${id}`,
			method: "PUT",
			data: { appointment: payload },
		});
		return response.appointment;
	},

	/**
	 * Appointment category operations
	 */
	category: {
		/**
		 * Get an appointment category by ID
		 */
		get: async (id: number): Promise<GetCCAppointmentCategoryType> => {
			const response = await ccRequest<{
				appointmentCategory: GetCCAppointmentCategoryType;
			}>({
				url: `/appointmentCategories/${id}`,
				method: "GET",
			});
			return response.appointmentCategory;
		},

		/**
		 * Get all appointment categories
		 */
		all: async (): Promise<GetCCAppointmentCategoryType[]> => {
			const response = await ccRequest<{
				appointmentCategories: GetCCAppointmentCategoryType[];
			}>({
				url: "/appointmentCategories",
				method: "GET",
			});
			return response.appointmentCategories;
		},
	},
};

/**
 * Invoice operations
 */
export const invoiceReq = {
	/**
	 * Get invoices by IDs
	 */
	get: async (ids: number[]): Promise<GetInvoiceType[]> => {
		const response = await ccRequest<{ invoices: GetInvoiceType[] }>({
			url: `/invoices?${idsToQueryString(ids)}`,
			method: "GET",
		});
		return response.invoices;
	},
};

/**
 * Payment operations
 */
export const paymentReq = {
	/**
	 * Get payments by IDs
	 */
	get: async (ids: number[]): Promise<GetPaymentType[]> => {
		const response = await ccRequest<{ payments: GetPaymentType[] }>({
			url: `/payments?${idsToQueryString(ids)}`,
			method: "GET",
		});
		return response.payments;
	},
};
